import { Article, Category } from '@/types';

const generateId = () => Math.random().toString(36).substring(2, 15);

const generateArticle = (
  title: string,
  category: Category,
  isFeatured = false,
  isBreaking = false,
  isTrending = false
): Article => ({
  id: generateId(),
  title,
  summary: `${title} - Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, nisl eget aliquam ultricies.`,
  content: `
    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, nisl eget aliquam ultricies, nisl nisl aliquam nisl, eget aliquam nisl nisl eget.</p>
    <p><PERSON><PERSON><PERSON> euismod, nisl eget aliquam ultricies, nisl nisl aliquam nisl, eget aliquam nisl nisl eget. Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
    <h2>The Impact</h2>
    <p><PERSON><PERSON><PERSON> euismod, nisl eget aliquam ultricies, nisl nisl aliquam nisl, eget aliquam nisl nisl eget. Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, nisl eget aliquam ultricies, nisl nisl aliquam nisl, eget aliquam nisl nisl eget.</p>
    <h2>What's Next</h2>
    <p>Nullam euismod, nisl eget aliquam ultricies, nisl nisl aliquam nisl, eget aliquam nisl nisl eget. Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
  `,
  category,
  imageUrl: `https://source.unsplash.com/random/800x600?${category.toLowerCase()}`,
  author: 'John Doe',
  publishedAt: new Date().toISOString(),
  isFeatured,
  isBreaking,
  isTrending,
  views: Math.floor(Math.random() * 1000) + 100,
  comments: []
});

// Generate articles for each category
export const mockArticles: Article[] = [
  // Featured articles
  generateArticle('Global Leaders Gather for Climate Summit', 'World', true, false, true),
  generateArticle('Tech Giant Unveils Revolutionary AI Tool', 'Tech', true, false, false),
  generateArticle('Historic Peace Agreement Signed', 'Politics', true, false, false),
  generateArticle('Olympic Medal Tally: Country Rankings', 'Sports', true, false, true),
  generateArticle('Blockbuster Movie Breaks Box Office Records', 'Entertainment', true, false, false),
  
  // Breaking News
  generateArticle('Breaking: Earthquake Hits Major City', 'World', false, true, true),
  generateArticle('Breaking: Stock Market Plunges Amid Economic Concerns', 'World', false, true, false),
  
  // Trending stories
  generateArticle('Viral Social Media Challenge Raises Millions for Charity', 'Entertainment', false, false, true),
  generateArticle('New Study Reveals Health Benefits of Popular Diet', 'World', false, false, true),
  
  // Regular articles - Politics
  generateArticle('Election Results: Incumbent President Re-elected', 'Politics'),
  generateArticle('New Legislation Aims to Reform Healthcare System', 'Politics'),
  generateArticle('Opposition Party Announces Policy Platform', 'Politics'),
  
  // Regular articles - Tech
  generateArticle('Smartphone Sales Decline for Third Quarter', 'Tech'),
  generateArticle('Cybersecurity Threats on the Rise, Experts Warn', 'Tech'),
  generateArticle('New Electric Vehicle Battery Promises 1000km Range', 'Tech'),
  
  // Regular articles - World
  generateArticle('Inflation Rates Reach Decade High in Multiple Countries', 'World'),
  generateArticle('Tourism Rebounds as Travel Restrictions Ease', 'World'),
  generateArticle('International Aid Sent to Disaster-Struck Region', 'World'),
  
  // Regular articles - Sports
  generateArticle('Local Team Advances to Championship Finals', 'Sports'),
  generateArticle('Athlete Breaks World Record in Track Event', 'Sports'),
  generateArticle('Major Trade Shakes Up Basketball League', 'Sports'),
  
  // Regular articles - Entertainment
  generateArticle('Celebrity Couple Announces Engagement', 'Entertainment'),
  generateArticle('Award-Winning Director Reveals Next Project', 'Entertainment'),
  generateArticle('Music Festival Lineup Announced for Summer', 'Entertainment')
];

// Editor's picks
export const editorsPicksArticles = mockArticles.slice(0, 4);

// Advertisement data
export const advertisements = [
  {
    id: '1',
    title: 'Premium Subscription',
    description: 'Get unlimited access to all premium articles',
    imageUrl: 'https://source.unsplash.com/random/300x250?business',
    link: '#subscribe'
  },
  {
    id: '2',
    title: 'Summer Sale',
    description: 'Exclusive deals for summer',
    imageUrl: 'https://source.unsplash.com/random/300x250?shopping',
    link: '#shop'
  }
];