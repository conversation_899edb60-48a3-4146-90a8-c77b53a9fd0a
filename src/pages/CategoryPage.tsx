import { useParams } from "react-router-dom";
import { useEffect, useState } from "react";
import Layout from "@/components/layout/Layout";
import { mockArticles, editorsPicksArticles, advertisements } from "@/data/mockData";
import { Article, Category } from "@/types";
import ArticleCard from "@/components/news/ArticleCard";
import EditorsPicks from "@/components/news/EditorsPicks";
import AdvertisementPanel from "@/components/news/AdvertisementPanel";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search } from "lucide-react";

export default function CategoryPage() {
  const { categoryName } = useParams<{ categoryName: string }>();
  const [articles, setArticles] = useState<Article[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  
  useEffect(() => {
    // In a real application, this would be an API call
    if (categoryName) {
      const formattedCategoryName = categoryName.charAt(0).toUpperCase() + categoryName.slice(1) as Category;
      const filteredArticles = mockArticles.filter(
        (article) => article.category.toLowerCase() === categoryName.toLowerCase()
      );
      setArticles(filteredArticles);
    }
  }, [categoryName]);
  
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Filter articles by search query
    const filteredArticles = mockArticles.filter(
      (article) => 
        article.category.toLowerCase() === categoryName?.toLowerCase() &&
        (article.title.toLowerCase().includes(searchQuery.toLowerCase()) || 
         article.summary.toLowerCase().includes(searchQuery.toLowerCase()))
    );
    setArticles(filteredArticles);
  };
  
  const categoryTitle = categoryName ? categoryName.charAt(0).toUpperCase() + categoryName.slice(1) : "";
  
  return (
    <Layout>
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold">
            <span className="text-[#D32F2F]">{categoryTitle}</span> News
          </h1>
          <form onSubmit={handleSearch} className="flex w-full max-w-sm items-center space-x-2">
            <Input
              type="search"
              placeholder={`Search in ${categoryTitle}...`}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Button type="submit" className="bg-[#D32F2F] hover:bg-red-700">
              <Search size={18} />
            </Button>
          </form>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-8">
            {articles.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {articles.map((article) => (
                  <ArticleCard key={article.id} article={article} />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <p className="text-gray-500">No articles found in this category.</p>
              </div>
            )}
          </div>
          
          {/* Sidebar */}
          <div className="lg:col-span-4 space-y-6">
            <AdvertisementPanel advertisements={advertisements} />
            <EditorsPicks articles={editorsPicksArticles} />
          </div>
        </div>
      </div>
    </Layout>
  );
}