import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { commentService } from '@/lib/database';
import { Comment } from '@/types';
import { toast } from 'sonner';

// Hook for fetching comments for an article
export function useComments(articleId: string) {
  return useQuery({
    queryKey: ['comments', articleId],
    queryFn: () => commentService.getComments(articleId),
    enabled: !!articleId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Hook for creating comments
export function useCreateComment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: commentService.createComment,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['comments', data.article_id] });
      toast.success('Comment posted successfully!');
    },
    onError: (error: Error) => {
      toast.error(`Failed to post comment: ${error.message}`);
    },
  });
}

// Hook for updating comments
export function useUpdateComment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, content }: { id: string; content: string }) =>
      commentService.updateComment(id, content),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['comments'] });
      toast.success('Comment updated successfully!');
    },
    onError: (error: Error) => {
      toast.error(`Failed to update comment: ${error.message}`);
    },
  });
}

// Hook for deleting comments
export function useDeleteComment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: commentService.deleteComment,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['comments'] });
      toast.success('Comment deleted successfully!');
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete comment: ${error.message}`);
    },
  });
}
