import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { articleService } from '@/lib/database';
import { Article, Category } from '@/types';
import { toast } from 'sonner';

// Hook for fetching articles with filters
export function useArticles(options: {
  page?: number;
  limit?: number;
  category?: Category;
  featured?: boolean;
  breaking?: boolean;
  trending?: boolean;
  search?: string;
} = {}) {
  return useQuery({
    queryKey: ['articles', options],
    queryFn: () => articleService.getArticles(options),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for fetching a single article
export function useArticle(id: string) {
  return useQuery({
    queryKey: ['article', id],
    queryFn: () => articleService.getArticle(id),
    enabled: !!id,
  });
}

// Hook for fetching articles by category
export function useArticlesByCategory(category: Category, limit = 10) {
  return useQuery({
    queryKey: ['articles', 'category', category, limit],
    queryFn: () => articleService.getArticlesByCategory(category, limit),
    staleTime: 5 * 60 * 1000,
  });
}

// Hook for creating articles
export function useCreateArticle() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: articleService.createArticle,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['articles'] });
      toast.success('Article created successfully!');
    },
    onError: (error: Error) => {
      toast.error(`Failed to create article: ${error.message}`);
    },
  });
}

// Hook for updating articles
export function useUpdateArticle() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<Article> }) =>
      articleService.updateArticle(id, updates),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['articles'] });
      queryClient.invalidateQueries({ queryKey: ['article', data.id] });
      toast.success('Article updated successfully!');
    },
    onError: (error: Error) => {
      toast.error(`Failed to update article: ${error.message}`);
    },
  });
}

// Hook for deleting articles
export function useDeleteArticle() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: articleService.deleteArticle,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['articles'] });
      toast.success('Article deleted successfully!');
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete article: ${error.message}`);
    },
  });
}

// Hook for incrementing article views
export function useIncrementViews() {
  return useMutation({
    mutationFn: articleService.incrementViews,
    onError: (error: Error) => {
      console.error('Failed to increment views:', error);
    },
  });
}

// Hook for featured articles
export function useFeaturedArticles(limit = 5) {
  return useQuery({
    queryKey: ['articles', 'featured', limit],
    queryFn: () => articleService.getArticles({ featured: true, limit }),
    staleTime: 5 * 60 * 1000,
  });
}

// Hook for breaking news
export function useBreakingNews(limit = 5) {
  return useQuery({
    queryKey: ['articles', 'breaking', limit],
    queryFn: () => articleService.getArticles({ breaking: true, limit }),
    staleTime: 1 * 60 * 1000, // 1 minute for breaking news
  });
}

// Hook for trending articles
export function useTrendingArticles(limit = 5) {
  return useQuery({
    queryKey: ['articles', 'trending', limit],
    queryFn: () => articleService.getArticles({ trending: true, limit }),
    staleTime: 5 * 60 * 1000,
  });
}

// Hook for search functionality
export function useSearchArticles() {
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  const searchResults = useQuery({
    queryKey: ['articles', 'search', debouncedSearchTerm],
    queryFn: () => articleService.getArticles({ search: debouncedSearchTerm }),
    enabled: debouncedSearchTerm.length > 2,
    staleTime: 2 * 60 * 1000,
  });

  return {
    searchTerm,
    setSearchTerm,
    searchResults: searchResults.data?.articles || [],
    isSearching: searchResults.isLoading,
    searchError: searchResults.error,
  };
}
