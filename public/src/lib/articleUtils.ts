import { Article } from '@/types';

// Convert database article to display format
export function formatArticleForDisplay(article: any): Article & {
  // Legacy format compatibility
  imageUrl: string;
  author: string;
  publishedAt: string;
  isFeatured: boolean;
  isBreaking: boolean;
  isTrending: boolean;
  comments: any[];
} {
  return {
    ...article,
    // Legacy format compatibility
    imageUrl: article.image_url,
    author: article.author?.full_name || article.author?.username || 'Unknown Author',
    publishedAt: article.published_at,
    isFeatured: article.is_featured,
    isBreaking: article.is_breaking,
    isTrending: article.is_trending,
    comments: article.comments || [],
  };
}

// Convert display format to database format
export function formatArticleForDatabase(article: any): Partial<Article> {
  const dbArticle: any = { ...article };
  
  // Remove legacy fields
  delete dbArticle.imageUrl;
  delete dbArticle.author;
  delete dbArticle.publishedAt;
  delete dbArticle.isFeatured;
  delete dbArticle.isBreaking;
  delete dbArticle.isTrending;
  delete dbArticle.comments;
  
  // Map to database fields
  if (article.imageUrl) dbArticle.image_url = article.imageUrl;
  if (article.publishedAt) dbArticle.published_at = article.publishedAt;
  if (article.isFeatured !== undefined) dbArticle.is_featured = article.isFeatured;
  if (article.isBreaking !== undefined) dbArticle.is_breaking = article.isBreaking;
  if (article.isTrending !== undefined) dbArticle.is_trending = article.isTrending;
  
  return dbArticle;
}

// Format articles array for display
export function formatArticlesForDisplay(articles: any[]): Array<Article & {
  imageUrl: string;
  author: string;
  publishedAt: string;
  isFeatured: boolean;
  isBreaking: boolean;
  isTrending: boolean;
  comments: any[];
}> {
  return articles.map(formatArticleForDisplay);
}
