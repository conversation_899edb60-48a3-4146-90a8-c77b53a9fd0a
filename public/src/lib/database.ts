import { supabase } from './supabase';
import {
  Article,
  Comment,
  Category,
  Bookmark,
  NewsletterSubscriber,
  Advertisement,
  WebsiteSetting,
  NewsletterTemplate,
  NewsletterCampaign,
  UserProfile
} from '@/types';

// Article operations
export const articleService = {
  // Get all articles with pagination and filters
  async getArticles(options: {
    page?: number;
    limit?: number;
    category?: Category;
    featured?: boolean;
    breaking?: boolean;
    trending?: boolean;
    search?: string;
  } = {}) {
    const { page = 1, limit = 10, category, featured, breaking, trending, search } = options;
    const offset = (page - 1) * limit;

    let query = supabase
      .from('articles')
      .select(`
        *,
        author:user_profiles(id, username, full_name, avatar_url)
      `)
      .order('published_at', { ascending: false });

    if (category) {
      query = query.eq('category', category);
    }

    if (featured !== undefined) {
      query = query.eq('is_featured', featured);
    }

    if (breaking !== undefined) {
      query = query.eq('is_breaking', breaking);
    }

    if (trending !== undefined) {
      query = query.eq('is_trending', trending);
    }

    if (search) {
      query = query.or(`title.ilike.%${search}%,summary.ilike.%${search}%,content.ilike.%${search}%`);
    }

    const { data, error, count } = await query
      .range(offset, offset + limit - 1)
      .limit(limit);

    if (error) throw error;

    return {
      articles: data || [],
      totalCount: count || 0,
      hasMore: (count || 0) > offset + limit
    };
  },

  // Get single article by ID
  async getArticle(id: string) {
    const { data, error } = await supabase
      .from('articles')
      .select(`
        *,
        author:user_profiles(id, username, full_name, avatar_url),
        comments(
          *,
          user_profile:user_profiles(id, username, full_name, avatar_url)
        )
      `)
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  },

  // Create new article
  async createArticle(article: Omit<Article, 'id' | 'created_at' | 'updated_at' | 'views'>) {
    const { data, error } = await supabase
      .from('articles')
      .insert([article])
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Update article
  async updateArticle(id: string, updates: Partial<Article>) {
    const { data, error } = await supabase
      .from('articles')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Delete article
  async deleteArticle(id: string) {
    const { error } = await supabase
      .from('articles')
      .delete()
      .eq('id', id);

    if (error) throw error;
  },

  // Increment article views
  async incrementViews(id: string) {
    const { error } = await supabase.rpc('increment_article_views', {
      article_uuid: id
    });

    if (error) throw error;
  },

  // Get articles by category
  async getArticlesByCategory(category: Category, limit = 10) {
    const { data, error } = await supabase
      .from('articles')
      .select(`
        *,
        author:user_profiles(id, username, full_name, avatar_url)
      `)
      .eq('category', category)
      .order('published_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data || [];
  }
};

// Comment operations
export const commentService = {
  // Get comments for an article
  async getComments(articleId: string) {
    const { data, error } = await supabase
      .from('comments')
      .select(`
        *,
        user_profile:user_profiles(id, username, full_name, avatar_url)
      `)
      .eq('article_id', articleId)
      .order('created_at', { ascending: true });

    if (error) throw error;
    return data || [];
  },

  // Create new comment
  async createComment(comment: Omit<Comment, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('comments')
      .insert([comment])
      .select(`
        *,
        user_profile:user_profiles(id, username, full_name, avatar_url)
      `)
      .single();

    if (error) throw error;
    return data;
  },

  // Update comment
  async updateComment(id: string, content: string) {
    const { data, error } = await supabase
      .from('comments')
      .update({ content })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Delete comment
  async deleteComment(id: string) {
    const { error } = await supabase
      .from('comments')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }
};

// Bookmark operations
export const bookmarkService = {
  // Get user bookmarks
  async getUserBookmarks(userId: string) {
    const { data, error } = await supabase
      .from('bookmarks')
      .select(`
        *,
        article:articles(
          *,
          author:user_profiles(id, username, full_name, avatar_url)
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  },

  // Check if article is bookmarked
  async isBookmarked(userId: string, articleId: string) {
    const { data, error } = await supabase
      .from('bookmarks')
      .select('id')
      .eq('user_id', userId)
      .eq('article_id', articleId)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return !!data;
  },

  // Add bookmark
  async addBookmark(userId: string, articleId: string) {
    const { data, error } = await supabase
      .from('bookmarks')
      .insert([{ user_id: userId, article_id: articleId }])
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Remove bookmark
  async removeBookmark(userId: string, articleId: string) {
    const { error } = await supabase
      .from('bookmarks')
      .delete()
      .eq('user_id', userId)
      .eq('article_id', articleId);

    if (error) throw error;
  }
};

// Newsletter operations
export const newsletterService = {
  // Subscribe to newsletter
  async subscribe(email: string) {
    const { data, error } = await supabase
      .from('newsletter_subscribers')
      .insert([{ email }])
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Unsubscribe from newsletter
  async unsubscribe(email: string) {
    const { error } = await supabase
      .from('newsletter_subscribers')
      .update({ is_active: false })
      .eq('email', email);

    if (error) throw error;
  },

  // Get all subscribers (admin only)
  async getSubscribers() {
    const { data, error } = await supabase
      .from('newsletter_subscribers')
      .select('*')
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  },

  // Get subscriber statistics
  async getSubscriberStats() {
    const { data: total, error: totalError } = await supabase
      .from('newsletter_subscribers')
      .select('id', { count: 'exact' });

    const { data: active, error: activeError } = await supabase
      .from('newsletter_subscribers')
      .select('id', { count: 'exact' })
      .eq('is_active', true);

    if (totalError || activeError) throw totalError || activeError;

    return {
      total: total?.length || 0,
      active: active?.length || 0,
      unsubscribed: (total?.length || 0) - (active?.length || 0)
    };
  }
};

// Advertisement operations
export const advertisementService = {
  // Get all advertisements
  async getAdvertisements(options: {
    active?: boolean;
    position?: string;
  } = {}) {
    const { active, position } = options;

    let query = supabase
      .from('advertisements')
      .select(`
        *,
        creator:user_profiles(id, username, full_name, avatar_url)
      `)
      .order('created_at', { ascending: false });

    if (active !== undefined) {
      query = query.eq('is_active', active);
    }

    if (position) {
      query = query.eq('position', position);
    }

    const { data, error } = await query;

    if (error) throw error;
    return data || [];
  },

  // Get single advertisement
  async getAdvertisement(id: string) {
    const { data, error } = await supabase
      .from('advertisements')
      .select(`
        *,
        creator:user_profiles(id, username, full_name, avatar_url)
      `)
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  },

  // Create advertisement
  async createAdvertisement(ad: Omit<Advertisement, 'id' | 'created_at' | 'updated_at' | 'clicks' | 'impressions'>) {
    const { data, error } = await supabase
      .from('advertisements')
      .insert([ad])
      .select(`
        *,
        creator:user_profiles(id, username, full_name, avatar_url)
      `)
      .single();

    if (error) throw error;
    return data;
  },

  // Update advertisement
  async updateAdvertisement(id: string, updates: Partial<Advertisement>) {
    const { data, error } = await supabase
      .from('advertisements')
      .update(updates)
      .eq('id', id)
      .select(`
        *,
        creator:user_profiles(id, username, full_name, avatar_url)
      `)
      .single();

    if (error) throw error;
    return data;
  },

  // Delete advertisement
  async deleteAdvertisement(id: string) {
    const { error } = await supabase
      .from('advertisements')
      .delete()
      .eq('id', id);

    if (error) throw error;
  },

  // Increment advertisement clicks
  async incrementClicks(id: string) {
    const { error } = await supabase.rpc('increment_ad_clicks', {
      ad_uuid: id
    });

    if (error) throw error;
  },

  // Increment advertisement impressions
  async incrementImpressions(id: string) {
    const { error } = await supabase.rpc('increment_ad_impressions', {
      ad_uuid: id
    });

    if (error) throw error;
  },

  // Get advertisement statistics
  async getAdvertisementStats() {
    const { data, error } = await supabase
      .from('advertisements')
      .select('clicks, impressions, is_active');

    if (error) throw error;

    const stats = data?.reduce((acc, ad) => {
      acc.totalClicks += ad.clicks;
      acc.totalImpressions += ad.impressions;
      if (ad.is_active) acc.activeAds++;
      return acc;
    }, { totalClicks: 0, totalImpressions: 0, activeAds: 0 });

    return {
      totalAds: data?.length || 0,
      activeAds: stats?.activeAds || 0,
      totalClicks: stats?.totalClicks || 0,
      totalImpressions: stats?.totalImpressions || 0,
      ctr: stats?.totalImpressions ? ((stats.totalClicks / stats.totalImpressions) * 100).toFixed(2) : '0'
    };
  }
};

// Website settings operations
export const settingsService = {
  // Get all settings
  async getSettings() {
    const { data, error } = await supabase
      .from('website_settings')
      .select('*')
      .order('setting_key');

    if (error) throw error;
    return data || [];
  },

  // Get setting by key
  async getSetting(key: string) {
    const { data, error } = await supabase
      .from('website_settings')
      .select('*')
      .eq('setting_key', key)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return data;
  },

  // Update setting
  async updateSetting(key: string, value: string, updatedBy: string) {
    const { data, error } = await supabase
      .from('website_settings')
      .upsert([{
        setting_key: key,
        setting_value: value,
        updated_by: updatedBy
      }])
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Update multiple settings
  async updateSettings(settings: Array<{key: string, value: string}>, updatedBy: string) {
    const updates = settings.map(setting => ({
      setting_key: setting.key,
      setting_value: setting.value,
      updated_by: updatedBy
    }));

    const { data, error } = await supabase
      .from('website_settings')
      .upsert(updates)
      .select();

    if (error) throw error;
    return data;
  }
};

// User management operations
export const userService = {
  // Get all users with pagination
  async getUsers(options: {
    page?: number;
    limit?: number;
    role?: string;
    search?: string;
  } = {}) {
    const { page = 1, limit = 10, role, search } = options;
    const offset = (page - 1) * limit;

    let query = supabase
      .from('user_profiles')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false });

    if (role && role !== 'all') {
      query = query.eq('role', role);
    }

    if (search) {
      query = query.or(`username.ilike.%${search}%,full_name.ilike.%${search}%,email.ilike.%${search}%`);
    }

    const { data, error, count } = await query
      .range(offset, offset + limit - 1);

    if (error) throw error;

    return {
      users: data || [],
      totalCount: count || 0,
      hasMore: (count || 0) > offset + limit
    };
  },

  // Get single user
  async getUser(id: string) {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  },

  // Update user
  async updateUser(id: string, updates: Partial<UserProfile>) {
    const { data, error } = await supabase
      .from('user_profiles')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Delete user (admin only)
  async deleteUser(id: string) {
    const { error } = await supabase
      .from('user_profiles')
      .delete()
      .eq('id', id);

    if (error) throw error;
  },

  // Get user statistics
  async getUserStats() {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('role');

    if (error) throw error;

    const stats = data?.reduce((acc, user) => {
      acc[user.role] = (acc[user.role] || 0) + 1;
      acc.total++;
      return acc;
    }, { total: 0, admin: 0, editor: 0, writer: 0, user: 0 });

    return stats || { total: 0, admin: 0, editor: 0, writer: 0, user: 0 };
  }
};
