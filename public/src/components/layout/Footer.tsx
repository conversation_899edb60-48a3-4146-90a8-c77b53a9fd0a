import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Facebook,
  Twitter,
  Instagram,
  Youtube,
  Mail,
  Phone,
  MapPin,
} from "lucide-react";
import { Link } from "react-router-dom";
import { useState } from "react";
import { newsletterService } from "@/lib/database";
import { toast } from "sonner";

export default function Footer() {
  const year = new Date().getFullYear();
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);

  const handleNewsletterSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email.trim()) {
      toast.error('Please enter your email address');
      return;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      toast.error('Please enter a valid email address');
      return;
    }

    setLoading(true);
    try {
      await newsletterService.subscribe(email);
      toast.success('Successfully subscribed to newsletter!');
      setEmail("");
    } catch (error: any) {
      console.error('Newsletter subscription error:', error);
      if (error.message?.includes('duplicate')) {
        toast.error('You are already subscribed to our newsletter');
      } else {
        toast.error('Failed to subscribe. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <footer className="bg-[#121212] text-white">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 py-10">
          <div>
            <h3 className="text-2xl font-bold mb-4 text-[#D32F2F]">THE SACH PATRA</h3>
            <p className="mb-4">
              Your trusted source for the latest news on politics, tech, world, sports,
              and entertainment.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="hover:text-[#D32F2F]">
                <Facebook size={20} />
              </a>
              <a href="#" className="hover:text-[#D32F2F]">
                <Twitter size={20} />
              </a>
              <a href="#" className="hover:text-[#D32F2F]">
                <Instagram size={20} />
              </a>
              <a href="#" className="hover:text-[#D32F2F]">
                <Youtube size={20} />
              </a>
            </div>
          </div>

          <div>
            <h4 className="text-lg font-bold mb-4">Categories</h4>
            <ul className="space-y-2">
              <li>
                <Link to="/category/politics" className="hover:text-[#D32F2F]">
                  Politics
                </Link>
              </li>
              <li>
                <Link to="/category/tech" className="hover:text-[#D32F2F]">
                  Technology
                </Link>
              </li>
              <li>
                <Link to="/category/world" className="hover:text-[#D32F2F]">
                  World
                </Link>
              </li>
              <li>
                <Link to="/category/sports" className="hover:text-[#D32F2F]">
                  Sports
                </Link>
              </li>
              <li>
                <Link to="/category/entertainment" className="hover:text-[#D32F2F]">
                  Entertainment
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="text-lg font-bold mb-4">Contact Us</h4>
            <ul className="space-y-2">
              <li className="flex items-center space-x-2">
                <Mail size={16} />
                <span><EMAIL></span>
              </li>
              <li className="flex items-center space-x-2">
                <Phone size={16} />
                <span>+****************</span>
              </li>
              <li className="flex items-center space-x-2">
                <MapPin size={16} />
                <span>123 News Street, Media City</span>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="text-lg font-bold mb-4">Newsletter</h4>
            <p className="mb-4">
              Subscribe to our newsletter for the latest updates and exclusive
              content.
            </p>
            <form onSubmit={handleNewsletterSubmit} className="flex space-x-2">
              <Input
                type="email"
                placeholder="Your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="bg-gray-800 border-gray-700 text-white placeholder:text-gray-400"
                required
              />
              <Button
                type="submit"
                className="bg-[#D32F2F] hover:bg-red-700"
                disabled={loading}
              >
                {loading ? 'Subscribing...' : 'Subscribe'}
              </Button>
            </form>
          </div>
        </div>

        <div className="py-6 border-t border-gray-800 text-center text-sm">
          <p>
            &copy; {year} The Sach Patra. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}