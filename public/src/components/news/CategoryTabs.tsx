import { Article, Category } from "@/types";
import { Tabs, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import ArticleCard from "./ArticleCard";

interface CategoryTabsProps {
  articles: Article[];
}

export default function CategoryTabs({ articles }: CategoryTabsProps) {
  const categories: Category[] = ["Politics", "Tech", "World", "Sports", "Entertainment"];
  
  const getArticlesByCategory = (category: Category) => {
    return articles.filter((article) => article.category === category).slice(0, 4);
  };

  return (
    <Tabs defaultValue="Politics" className="w-full">
      <TabsList className="w-full grid grid-cols-5">
        {categories.map((category) => (
          <TabsTrigger key={category} value={category} className="text-sm">
            {category}
          </TabsTrigger>
        ))}
      </TabsList>
      {categories.map((category) => (
        <TabsContent key={category} value={category}>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {getArticlesByCategory(category).map((article) => (
              <ArticleCard key={article.id} article={article} />
            ))}
          </div>
        </TabsContent>
      ))}
    </Tabs>
  );
}