import { Article } from "@/types";
import ArticleCard from "./ArticleCard";

interface EditorsPicksProps {
  articles: Article[];
}

export default function EditorsPicks({ articles }: EditorsPicksProps) {
  return (
    <div className="bg-gray-50 p-4 rounded-lg">
      <h2 className="text-xl font-bold mb-4 pb-2 border-b border-gray-200">
        <span className="text-[#D32F2F]">Editor's</span> Picks
      </h2>
      <div className="space-y-4">
        {articles.map((article) => (
          <ArticleCard key={article.id} article={article} variant="compact" />
        ))}
      </div>
    </div>
  );
}