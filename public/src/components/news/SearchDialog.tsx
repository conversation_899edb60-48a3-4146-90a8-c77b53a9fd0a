import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Search, Clock, TrendingUp } from 'lucide-react';
import { useSearchArticles } from '@/hooks/useArticles';
import { formatArticlesForDisplay } from '@/lib/articleUtils';
import { Category } from '@/types';

interface SearchDialogProps {
  children: React.ReactNode;
}

export default function SearchDialog({ children }: SearchDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | 'all'>('all');
  const navigate = useNavigate();
  
  const { searchTerm, setSearchTerm, searchResults, isSearching } = useSearchArticles();
  
  const categories: (Category | 'all')[] = ['all', 'Politics', 'Tech', 'World', 'Sports', 'Entertainment'];
  
  const filteredResults = selectedCategory === 'all' 
    ? searchResults 
    : searchResults.filter(article => article.category === selectedCategory);
  
  const formattedResults = formatArticlesForDisplay(filteredResults);

  const handleArticleClick = (articleId: string) => {
    setIsOpen(false);
    navigate(`/article/${articleId}`);
  };

  const handleCategoryClick = (category: Category) => {
    setIsOpen(false);
    navigate(`/category/${category.toLowerCase()}`);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Search Articles</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Search Input */}
          <div className="relative">
            <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search for articles..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          {/* Category Filter */}
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <Badge
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                className="cursor-pointer"
                onClick={() => setSelectedCategory(category)}
              >
                {category === 'all' ? 'All Categories' : category}
              </Badge>
            ))}
          </div>
          
          {/* Search Results */}
          <div className="max-h-[400px] overflow-y-auto">
            {searchTerm.length > 2 ? (
              isSearching ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#D32F2F] mx-auto"></div>
                  <p className="mt-2 text-sm text-gray-500">Searching...</p>
                </div>
              ) : formattedResults.length > 0 ? (
                <div className="space-y-3">
                  {formattedResults.slice(0, 10).map((article) => (
                    <div
                      key={article.id}
                      className="flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer"
                      onClick={() => handleArticleClick(article.id)}
                    >
                      <img
                        src={article.imageUrl}
                        alt={article.title}
                        className="w-16 h-16 object-cover rounded"
                      />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <Badge className="bg-[#1976D2] hover:bg-blue-700 text-xs">
                            {article.category}
                          </Badge>
                          {article.isTrending && (
                            <TrendingUp className="h-3 w-3 text-[#D32F2F]" />
                          )}
                        </div>
                        <h4 className="font-medium text-sm line-clamp-2 mb-1">
                          {article.title}
                        </h4>
                        <div className="flex items-center text-xs text-gray-500">
                          <Clock className="h-3 w-3 mr-1" />
                          <span>{new Date(article.publishedAt).toLocaleDateString()}</span>
                          <span className="mx-2">•</span>
                          <span>{article.views} views</span>
                        </div>
                      </div>
                    </div>
                  ))}
                  {formattedResults.length > 10 && (
                    <div className="text-center py-2">
                      <p className="text-sm text-gray-500">
                        Showing 10 of {formattedResults.length} results
                      </p>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Search className="h-12 w-12 text-gray-300 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">No articles found</p>
                  <p className="text-xs text-gray-400">Try different keywords or categories</p>
                </div>
              )
            ) : (
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Quick Categories</h4>
                  <div className="grid grid-cols-2 gap-2">
                    {categories.slice(1).map((category) => (
                      <Button
                        key={category}
                        variant="outline"
                        size="sm"
                        className="justify-start"
                        onClick={() => handleCategoryClick(category as Category)}
                      >
                        {category}
                      </Button>
                    ))}
                  </div>
                </div>
                
                <div className="text-center py-4">
                  <Search className="h-8 w-8 text-gray-300 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">Start typing to search articles</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
