import { Article } from "@/types";
import { Link } from "react-router-dom";
import { TrendingUp } from "lucide-react";

interface TrendingArticlesProps {
  articles: Article[];
}

export default function TrendingArticles({ articles }: TrendingArticlesProps) {
  return (
    <div className="bg-white rounded-lg shadow-sm p-4">
      <div className="flex items-center space-x-2 mb-4">
        <TrendingUp className="h-5 w-5 text-[#D32F2F]" />
        <h2 className="text-xl font-bold">Trending Now</h2>
      </div>
      <div className="space-y-3">
        {articles.map((article, index) => (
          <Link
            key={article.id}
            to={`/article/${article.id}`}
            className="flex items-center space-x-3 hover:bg-gray-50 p-2 rounded-md transition-colors"
          >
            <span className="font-bold text-lg text-[#D32F2F]">{index + 1}</span>
            <div>
              <h3 className="font-medium line-clamp-2">{article.title}</h3>
              <p className="text-xs text-gray-500">{article.category}</p>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
}